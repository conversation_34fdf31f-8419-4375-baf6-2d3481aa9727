const mysql = require('mysql2/promise');
const fs = require('fs');
const path = require('path');

async function runMigration() {
  let connection;
  
  try {
    // Create connection
    connection = await mysql.createConnection({
      host: 'localhost',
      user: 'root',
      password: 'root',
      database: 'split_job',
      multipleStatements: true
    });

    console.log('Connected to database successfully');

    // Read the migration SQL file
    const migrationPath = path.join(__dirname, 'add-document-verification-columns.sql');
    const migrationSQL = fs.readFileSync(migrationPath, 'utf8');

    console.log('Running migration...');

    // Execute the migration
    const [results] = await connection.execute(migrationSQL);
    
    console.log('Migration completed successfully!');
    
    // Show the results
    if (Array.isArray(results)) {
      results.forEach((result, index) => {
        if (result && result.length > 0) {
          console.log(`Result ${index + 1}:`, result);
        }
      });
    }

  } catch (error) {
    console.error('Migration failed:', error);
    process.exit(1);
  } finally {
    if (connection) {
      await connection.end();
      console.log('Database connection closed');
    }
  }
}

// Run the migration
runMigration();
