import { useState } from 'react';

export default function MigratePage() {
  const [loading, setLoading] = useState(false);
  const [result, setResult] = useState(null);
  const [error, setError] = useState(null);

  const runMigration = async () => {
    setLoading(true);
    setError(null);
    setResult(null);

    try {
      const response = await fetch('/api/admin/migrate-database', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          secret: 'migrate-database-2024'
        }),
      });

      const data = await response.json();

      if (response.ok) {
        setResult(data);
      } else {
        setError(data.error || 'Migration failed');
      }
    } catch (err) {
      setError('Network error: ' + err.message);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-4xl mx-auto px-4">
        <h1 className="text-3xl font-bold mb-8">Database Migration</h1>
        
        <div className="bg-white rounded-lg shadow p-6 mb-6">
          <h2 className="text-xl font-semibold mb-4">Add Document Verification Columns</h2>
          <p className="text-gray-600 mb-4">
            This will add the missing columns to the contracts table to fix the document verification feature.
          </p>
          
          <button
            onClick={runMigration}
            disabled={loading}
            className="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 disabled:opacity-50"
          >
            {loading ? 'Running Migration...' : 'Run Migration'}
          </button>
        </div>

        {error && (
          <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
            <h3 className="text-red-800 font-semibold mb-2">Error</h3>
            <p className="text-red-700">{error}</p>
          </div>
        )}

        {result && (
          <div className="bg-green-50 border border-green-200 rounded-lg p-4 mb-6">
            <h3 className="text-green-800 font-semibold mb-4">Migration Results</h3>
            
            <div className="space-y-2 mb-4">
              {result.results.map((item, index) => (
                <div key={index} className="flex items-center gap-2">
                  {item.status === 'success' && <span className="text-green-600">✅</span>}
                  {item.status === 'skipped' && <span className="text-yellow-600">⚠️</span>}
                  {item.status === 'error' && <span className="text-red-600">❌</span>}
                  <span className="font-medium">{item.name}</span>
                  <span className="text-gray-600">- {item.message}</span>
                </div>
              ))}
            </div>

            <details className="mt-4">
              <summary className="cursor-pointer font-medium text-green-800">
                View Table Structure
              </summary>
              <div className="mt-2 bg-gray-100 p-3 rounded text-sm font-mono">
                <table className="w-full">
                  <thead>
                    <tr className="border-b">
                      <th className="text-left p-1">Field</th>
                      <th className="text-left p-1">Type</th>
                      <th className="text-left p-1">Null</th>
                      <th className="text-left p-1">Key</th>
                      <th className="text-left p-1">Default</th>
                    </tr>
                  </thead>
                  <tbody>
                    {result.tableStructure.map((col, index) => (
                      <tr key={index} className="border-b">
                        <td className="p-1">{col.Field}</td>
                        <td className="p-1">{col.Type}</td>
                        <td className="p-1">{col.Null}</td>
                        <td className="p-1">{col.Key}</td>
                        <td className="p-1">{col.Default || 'NULL'}</td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </details>
          </div>
        )}
      </div>
    </div>
  );
}
