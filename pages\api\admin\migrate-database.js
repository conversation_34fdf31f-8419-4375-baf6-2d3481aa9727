import { query } from "../../../lib/db";

export default async function handler(req, res) {
  if (req.method !== "POST") {
    return res.status(405).json({ error: "Method not allowed" });
  }

  // Simple security check - you can remove this after running the migration
  const { secret } = req.body;
  if (secret !== "migrate-database-2024") {
    return res.status(403).json({ error: "Unauthorized" });
  }

  try {
    console.log("Starting database migration...");
    
    const migrations = [
      {
        name: "Add seeker_documents_verified column",
        sql: "ALTER TABLE contracts ADD COLUMN seeker_documents_verified BOOLEAN DEFAULT FALSE"
      },
      {
        name: "Add referrer_documents_verified column", 
        sql: "ALTER TABLE contracts ADD COLUMN referrer_documents_verified BOOLEAN DEFAULT FALSE"
      },
      {
        name: "Add seeker_verified_at column",
        sql: "ALTER TABLE contracts ADD COLUMN seeker_verified_at TIMESTAMP NULL"
      },
      {
        name: "Add referrer_verified_at column",
        sql: "ALTER TABLE contracts ADD COLUMN referrer_verified_at TIMESTAMP NULL"
      },
      {
        name: "Add offer_letter_released column",
        sql: "ALTER TABLE contracts ADD COLUMN offer_letter_released BOOLEAN DEFAULT FALSE"
      },
      {
        name: "Add offer_letter_url column",
        sql: "ALTER TABLE contracts ADD COLUMN offer_letter_url VARCHAR(500)"
      },
      {
        name: "Add offer_letter_released_at column",
        sql: "ALTER TABLE contracts ADD COLUMN offer_letter_released_at TIMESTAMP NULL"
      }
    ];

    const results = [];

    for (const migration of migrations) {
      try {
        console.log(`Running: ${migration.name}`);
        await query(migration.sql);
        results.push({ 
          name: migration.name, 
          status: "success",
          message: "Column added successfully"
        });
        console.log(`✅ ${migration.name} - Success`);
      } catch (error) {
        if (error.code === 'ER_DUP_FIELDNAME') {
          results.push({ 
            name: migration.name, 
            status: "skipped",
            message: "Column already exists"
          });
          console.log(`⚠️ ${migration.name} - Column already exists`);
        } else {
          results.push({ 
            name: migration.name, 
            status: "error",
            message: error.message
          });
          console.log(`❌ ${migration.name} - Error: ${error.message}`);
        }
      }
    }

    // Get final table structure
    const tableStructure = await query("DESCRIBE contracts");

    console.log("Migration completed!");

    res.status(200).json({
      success: true,
      message: "Database migration completed",
      results: results,
      tableStructure: tableStructure
    });

  } catch (error) {
    console.error("Migration failed:", error);
    res.status(500).json({ 
      error: "Migration failed", 
      details: error.message 
    });
  }
}
