import { useState, useEffect } from "react";
import { useRouter } from "next/router";
import Layout from "../components/Layout";
import JobCard from "../components/JobCard";
import ApplicationCard from "../components/ApplicationCard";
import JobForm from "../components/JobForm";
import { Plus, Briefcase, Users, Clock, CheckCircle } from "lucide-react";

export default function Dashboard({ user, loading, logout }) {
  const router = useRouter();
  const [activeTab, setActiveTab] = useState("posts");
  const [myPosts, setMyPosts] = useState([]);
  const [myApplications, setMyApplications] = useState([]);
  const [showJobForm, setShowJobForm] = useState(false);
  const [editingJob, setEditingJob] = useState(null);
  const [stats, setStats] = useState({
    totalPosts: 0,
    totalApplications: 0,
    pendingContracts: 0,
    completedDeals: 0,
  });

  useEffect(() => {
    if (!loading && !user) {
      router.push("/login");
    } else if (user) {
      fetchDashboardData();
    }
  }, [user, loading]);

  const fetchDashboardData = async () => {
    try {
      // Fetch user's posts
      const postsRes = await fetch("/api/jobs/my-posts");
      const postsData = await postsRes.json();
      setMyPosts(postsData.posts || []);

      // Fetch user's applications
      const appsRes = await fetch("/api/applications");
      const appsData = await appsRes.json();
      setMyApplications(appsData.applications || []);

      // Calculate stats
      setStats({
        totalPosts: postsData.posts?.length || 0,
        totalApplications: appsData.applications?.length || 0,
        pendingContracts:
          appsData.applications?.filter((a) => a.status === "in_progress")
            .length || 0,
        completedDeals:
          appsData.applications?.filter((a) => a.status === "completed")
            .length || 0,
      });
    } catch (error) {
      console.error("Failed to fetch dashboard data:", error);
    }
  };

  const handleCreateJob = async (formData) => {
    try {
      const res = await fetch("/api/jobs/create", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(formData),
      });

      if (res.ok) {
        setShowJobForm(false);
        fetchDashboardData();
      } else {
        alert("Failed to create job post");
      }
    } catch (error) {
      alert("Failed to create job post");
    }
  };

  const handleEditJob = async (formData) => {
    try {
      const res = await fetch(`/api/jobs/${editingJob.id}`, {
        method: "PATCH",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(formData),
      });

      if (res.ok) {
        setEditingJob(null);
        fetchDashboardData();
      } else {
        alert("Failed to update job post");
      }
    } catch (error) {
      alert("Failed to update job post");
    }
  };

  if (loading) {
    return (
      <Layout user={user} logout={logout}>
        <div className="min-h-screen flex items-center justify-center">
          <div>Loading...</div>
        </div>
      </Layout>
    );
  }

  if (!user) {
    return null;
  }

  return (
    <Layout user={user} logout={logout}>
      <div className="min-h-screen bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 py-8">
          {/* Header */}
          <div className="mb-8">
            <h1 className="text-3xl font-bold mb-2">
              Welcome back, {user.full_name}!
            </h1>
            <p className="text-gray-600">
              {user.user_type === "seeker"
                ? "Manage your job posts and track applications"
                : "Browse opportunities and manage your referrals"}
            </p>
          </div>

          {/* Stats Cards */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-8">
            <div className="bg-white p-6 rounded-lg shadow">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-gray-500 text-sm">Total Posts</p>
                  <p className="text-2xl font-bold">{stats.totalPosts}</p>
                </div>
                <Briefcase className="text-gray-400" size={24} />
              </div>
            </div>
            <div className="bg-white p-6 rounded-lg shadow">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-gray-500 text-sm">Applications</p>
                  <p className="text-2xl font-bold">
                    {stats.totalApplications}
                  </p>
                </div>
                <Users className="text-gray-400" size={24} />
              </div>
            </div>
            <div className="bg-white p-6 rounded-lg shadow">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-gray-500 text-sm">In Progress</p>
                  <p className="text-2xl font-bold">{stats.pendingContracts}</p>
                </div>
                <Clock className="text-gray-400" size={24} />
              </div>
            </div>
            <div className="bg-white p-6 rounded-lg shadow">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-gray-500 text-sm">Completed</p>
                  <p className="text-2xl font-bold">{stats.completedDeals}</p>
                </div>
                <CheckCircle className="text-gray-400" size={24} />
              </div>
            </div>
          </div>

          {/* Tabs */}
          <div className="bg-white rounded-lg shadow">
            <div className="border-b">
              <div className="flex">
                <button
                  onClick={() => setActiveTab("posts")}
                  className={`px-6 py-4 font-medium ${
                    activeTab === "posts"
                      ? "border-b-2 border-black text-black"
                      : "text-gray-500 hover:text-gray-700"
                  }`}
                >
                  {user.user_type === "seeker" ? "My Posts" : "Available Posts"}
                </button>
                <button
                  onClick={() => setActiveTab("applications")}
                  className={`px-6 py-4 font-medium ${
                    activeTab === "applications"
                      ? "border-b-2 border-black text-black"
                      : "text-gray-500 hover:text-gray-700"
                  }`}
                >
                  {user.user_type === "seeker"
                    ? "Applications Received"
                    : "My Applications"}
                </button>
              </div>
            </div>

            <div className="p-6">
              {activeTab === "posts" ? (
                <div>
                  {user.user_type === "seeker" && (
                    <button
                      onClick={() => setShowJobForm(true)}
                      className="mb-6 px-6 py-3 bg-black text-white rounded-lg hover:bg-gray-800 transition-colors flex items-center gap-2"
                    >
                      <Plus size={20} /> Create New Post
                    </button>
                  )}

                  {myPosts.length === 0 ? (
                    <div className="text-center py-12 text-gray-500">
                      {user.user_type === "seeker"
                        ? "You haven't created any posts yet."
                        : "No posts available at the moment."}
                    </div>
                  ) : (
                    <div className="grid gap-4">
                      {myPosts.map((post) => (
                        <JobCard
                          key={post.id}
                          job={post}
                          user={user}
                          isOwner={true}
                          onEdit={() => setEditingJob(post)}
                        />
                      ))}
                    </div>
                  )}
                </div>
              ) : (
                <div>
                  {myApplications.length === 0 ? (
                    <div className="text-center py-12 text-gray-500">
                      No applications yet.
                    </div>
                  ) : (
                    <div className="grid gap-4">
                      {myApplications.map((application) => (
                        <ApplicationCard
                          key={application.id}
                          application={application}
                          user={user}
                          onUpdate={fetchDashboardData}
                        />
                      ))}
                    </div>
                  )}
                </div>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Job Form Modal */}
      {(showJobForm || editingJob) && (
        <JobForm
          initialData={editingJob}
          onSubmit={editingJob ? handleEditJob : handleCreateJob}
          onCancel={() => {
            setShowJobForm(false);
            setEditingJob(null);
          }}
        />
      )}
    </Layout>
  );
}
